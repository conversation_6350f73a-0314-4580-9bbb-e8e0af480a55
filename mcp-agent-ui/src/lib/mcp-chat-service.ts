/**
 * MCP Chat Service
 *
 * Bridges the existing MCP Multi-Agent backend with AI SDK UI components.
 * Provides streaming chat functionality with real-time tool execution visibility.
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>dapter } from 'ai';
import { MCPAgent, MCPClient } from 'mcp-use';
import { ChatOpenAI } from '@langchain/openai';
import type { CoreMessage } from 'ai';

export interface ChatOptions {
  conversationHistory?: CoreMessage[];
  enableToolVisibility?: boolean;
  maxSteps?: number;
  timeout?: number;
  servers?: string[];
}

export class MCPChatService {
  private agent: MCPAgent | null = null;
  private client: MCPClient | null = null;
  private llm: ChatOpenAI | null = null;
  private initialized = false;

  constructor() {
    // Initialize will be called on first use
  }

  /**
   * Initialize the MCP service with configuration
   */
  private async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.log('🔧 Initializing MCP Chat Service...');

      // Create MCP client with basic configuration
      // This uses a simplified setup that can be expanded later
      const mcpConfig = {
        mcpServers: {
          // Start with a basic server for demonstration
          filesystem: {
            command: 'npx',
            args: ['-y', '@modelcontextprotocol/server-filesystem', process.cwd()],
          },
        },
      };

      this.client = new MCPClient(mcpConfig);

      // Create OpenAI LLM client
      this.llm = new ChatOpenAI({
        model: 'gpt-4o',
        temperature: 0.1,
        openAIApiKey: process.env.OPENAI_API_KEY,
      });

      // Create MCP Agent
      this.agent = new MCPAgent({
        llm: this.llm,
        client: this.client,
        maxSteps: 5,
        verbose: false,
      });

      this.initialized = true;
      console.log('✅ MCP Chat Service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize MCP Chat Service:', error);
      throw new Error(`MCP initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Stream chat response using MCP Agent with AI SDK compatibility
   */
  async streamChat(query: string, options: ChatOptions = {}): Promise<Response> {
    // Ensure service is initialized
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.agent) {
      throw new Error('MCP Agent not initialized');
    }

    try {
      console.log(`🤖 Processing query: "${query.slice(0, 100)}${query.length > 100 ? '...' : ''}"`);

      // Build context from conversation history
      const contextualQuery = this.buildContextualQuery(query, options.conversationHistory);

      // Get streaming events from MCP Agent
      const streamEvents = this.agent.streamEvents(contextualQuery, options.maxSteps || 5);

      // Convert to AI SDK compatible format with tool visibility
      const aiSDKStream = options.enableToolVisibility
        ? this.streamEventsToAISDKWithTools(streamEvents)
        : this.streamEventsToAISDK(streamEvents);

      // Create readable stream
      const readableStream = this.createReadableStreamFromGenerator(aiSDKStream);

      // Return AI SDK compatible response
      return LangChainAdapter.toDataStreamResponse(readableStream);
    } catch (error) {
      console.error('❌ Error in MCP chat streaming:', error);
      throw error;
    }
  }

  /**
   * Convert stream events to AI SDK format with tool visibility
   */
  private async *streamEventsToAISDKWithTools(streamEvents: AsyncGenerator<any, void, void>) {
    for await (const event of streamEvents) {
      switch (event.event) {
        case 'on_chat_model_stream':
          if (event.data?.chunk?.text) {
            const textContent = event.data.chunk.text;
            if (typeof textContent === 'string' && textContent.length > 0) {
              yield textContent;
            }
          }
          break;

        case 'on_tool_start':
          yield `\n🔧 Using tool: ${event.name}\n`;
          break;

        case 'on_tool_end':
          yield `\n✅ Tool completed: ${event.name}\n`;
          break;
      }
    }
  }

  /**
   * Create readable stream from async generator
   */
  private createReadableStreamFromGenerator(
    generator: AsyncGenerator<string, void, void>,
  ): ReadableStream<string> {
    return new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of generator) {
            controller.enqueue(chunk);
          }
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });
  }

  /**
   * Convert stream events to AI SDK format (basic version)
   */
  private async *streamEventsToAISDK(streamEvents: AsyncGenerator<any, void, void>) {
    for await (const event of streamEvents) {
      if (event.event === 'on_chat_model_stream' && event.data?.chunk?.text) {
        const textContent = event.data.chunk.text;
        if (typeof textContent === 'string' && textContent.length > 0) {
          yield textContent;
        }
      }
    }
  }

  /**
   * Build contextual query from conversation history
   */
  private buildContextualQuery(query: string, history?: CoreMessage[]): string {
    if (!history || history.length === 0) {
      return query;
    }

    const contextParts = history.slice(-6).map(msg => { // Last 6 messages for context
      const role = msg.role === 'user' ? 'Human' : 'Assistant';
      return `${role}: ${msg.content}`;
    });

    return `Previous conversation:\n${contextParts.join('\n')}\n\nCurrent query: ${query}`;
  }

  /**
   * Get service health status
   */
  async getHealthStatus() {
    try {
      if (!this.initialized) {
        return { status: 'not_initialized', healthy: false };
      }

      return {
        status: 'healthy',
        healthy: true,
        agent_initialized: !!this.agent,
        client_initialized: !!this.client,
        llm_initialized: !!this.llm,
      };
    } catch (error) {
      return {
        status: 'error',
        healthy: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up MCP Chat Service...');

      // Close MCP client connections
      if (this.client) {
        await this.client.closeAllSessions();
      }

      this.initialized = false;
      this.agent = null;
      this.client = null;
      this.llm = null;
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}
