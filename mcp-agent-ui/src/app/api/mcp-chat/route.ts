import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { NextRequest } from 'next/server';

// Allow streaming responses up to 60 seconds for MCP operations
export const maxDuration = 60;

/**
 * MCP-integrated chat endpoint
 * 
 * This endpoint bridges the AI SDK UI with the existing MCP Multi-Agent backend.
 * It provides streaming responses while integrating with multiple MCP servers.
 */
export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json();

    // Check if MCP integration is enabled
    const mcpEnabled = process.env.MCP_SERVER_ENABLED === 'true';
    const mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:3001';

    if (!mcpEnabled) {
      // Fallback to basic OpenAI integration
      const result = streamText({
        model: openai('gpt-4o'),
        messages,
        system: `You are a helpful AI assistant with MCP (Model Context Protocol) capabilities.

Note: MCP server integration is currently disabled. To enable full MCP functionality:
1. Set MCP_SERVER_ENABLED=true in your environment
2. Ensure MCP_SERVER_URL points to your running MCP Multi-Agent backend
3. Restart the development server

Current capabilities (without MCP):
- General conversation and assistance
- Code analysis and suggestions
- Technical documentation help
- Project planning and guidance

For full MCP capabilities including file operations, web search, package management, and task tracking, please enable MCP server integration.`,
      });

      return result.toDataStreamResponse();
    }

    // MCP Integration enabled - prepare for backend connection
    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      system: `You are an advanced AI assistant with access to Multiple MCP (Model Context Protocol) servers.

🔧 MCP CAPABILITIES ENABLED:
- File operations and code analysis
- Web search and research capabilities  
- Package management and documentation lookup
- Project management and task tracking
- Real-time tool execution and monitoring

🌐 MCP SERVER: ${mcpServerUrl}
📡 STATUS: Connected and ready for tool integration

When users request help, you can leverage these MCP capabilities:

1. **Code & Development**:
   - Analyze codebases and suggest improvements
   - Manage dependencies and packages
   - Execute file operations and code generation

2. **Research & Documentation**:
   - Perform web searches for latest information
   - Look up technical documentation
   - Research packages and libraries

3. **Project Management**:
   - Track tasks and project progress
   - Manage project documentation
   - Coordinate development workflows

4. **Real-time Operations**:
   - Execute tools and show progress
   - Monitor server health and status
   - Provide streaming updates

Always explain what MCP tools you're using and why, so users understand the enhanced capabilities.`,
      tools: {
        // TODO: Integrate actual MCP tools here
        // This will be implemented in the next phase
        mcpStatus: {
          description: 'Check MCP server status and available tools',
          parameters: {
            type: 'object',
            properties: {},
          },
          execute: async () => {
            // TODO: Implement actual MCP server status check
            return {
              status: 'connected',
              serverUrl: mcpServerUrl,
              availableTools: [
                'file-operations',
                'web-search', 
                'package-management',
                'task-tracking'
              ],
              message: 'MCP Multi-Agent backend is ready for integration'
            };
          },
        },
      },
    });

    return result.toDataStreamResponse();

  } catch (error) {
    console.error('MCP Chat API error:', error);
    
    // Provide helpful error information
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        return new Response(
          JSON.stringify({
            error: 'MCP Server Connection Failed',
            message: 'Could not connect to MCP Multi-Agent backend. Please ensure the server is running.',
            details: `Check that your MCP server is running at ${process.env.MCP_SERVER_URL || 'http://localhost:3001'}`
          }),
          { 
            status: 503,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }
    }

    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
